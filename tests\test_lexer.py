from utils import Tokenizer


def test_001():
    """Test basic identifier tokenization"""
    source = "abc"
    expected = "abc,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_002():
    """Test keywords recognition"""
    source = "func main if else while for let const"
    expected = "func,main,if,else,while,for,let,const,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_003():
    """Test integer literals"""
    source = "42 0 -17 007"
    expected = "42,0,-,17,007,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_004():
    """Test float literals"""
    source = "3.14 -2.5 0.0 42. 5."
    expected = "3.14,-,2.5,0.0,42.,5.,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_005():
    """Test boolean literals"""
    source = "true false"
    expected = "true,false,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_006():
    """Test unclosed string literal error"""
    source = '"Hello World'
    expected = "Unclosed String: Hello World"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_007():
    """Test illegal escape sequence error"""
    source = '"Hello \\x World"'
    expected = "Illegal Escape In String: Hello \\x"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_008():
    """Test error character (non-ASCII or invalid character)"""
    source = "let x = 5; @ invalid"
    expected = "let,x,=,5,;,Error Token @"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_009():
    """Test valid string literals with escape sequences"""
    source = '"Hello World" "Line 1\\nLine 2" "Quote: \\"text\\""'
    expected = "Hello World,Line 1\\nLine 2,Quote: \\\"text\\\",EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_009a():
    """Test string literals return content without quotes"""
    source = '"Hello World"'
    expected = "Hello World,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_009b():
    """Test empty string literal"""
    source = '""'
    expected = ",EOF"  # Empty string content
    assert Tokenizer(source).get_tokens_as_string() == expected


def test_010():
    """Test operators and separators"""
    source = "+ - * / % == != < <= > >= && || ! = -> >> ( ) [ ] { } , ; :"
    expected = "+,-,*,/,%,==,!=,<,<=,>,>=,&&,||,!,=,->,>>,(,),[,],{,},,,;,:,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_091():
    """Test basic integer and float literals"""
    source = "123 42.0 -5 0.007 3.14e-5"
    expected = "123,42.0,-,5,0.007,3.14e-5,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_092():
    """Test keywords recognition"""
    source = "func main if else while for let const bool int float string void return break continue in true false"
    expected = "func,main,if,else,while,for,let,const,bool,int,float,string,void,return,break,continue,in,true,false,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_093():
    """Test operators recognition"""
    source = "+ - * / % == != < <= > >= && || ! = : -> >>"
    expected = "+,-,*,/,%,==,!=,<,<=,>,>=,&&,||,!,=,:,->,>>,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_094():
    """Test separators recognition"""
    source = "()[]{};,."
    expected = "(,),[,],{,},;,,,.,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_095():
    """Test identifiers and mixed tokens"""
    source = "myVar _another_var_123 funcName(arg1, arg2) 42 + 3.14"
    expected = "myVar,_another_var_123,funcName,(,arg1,,,arg2,),42,+,3.14,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_096():
    """Test single line comments and whitespace handling"""
    source = "let x = 10; // This is a comment\n let y = 20;"
    expected = "let,x,=,10,;,let,y,=,20,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_097():
    """Test multi-line comments and nesting"""
    source = "/* This is an outer comment /* inner comment */ still outer */ func test()"
    expected = "func,test,(,),EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_098():
    """Test string literals with escape sequences"""
    source = "\"Hello World\" \"Line 1\\nLine 2\" \"Quote: \\\"text\\\"\" \"\""
    expected = "Hello World,Line 1\\nLine 2,Quote: \\\"text\\\",,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_099():
    """Test array literals"""
    source = "[1, 2, 3] [\"hello\", \"world\"] []"
    expected = "[,1,,,2,,,3,],[,hello,,,world,],[,],EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_100():
    """Test a small program snippet"""
    source = "const PI: float = 3.14;\nfunc calculateArea(radius: float) -> float { return PI * radius * radius; }"
    expected = "const,PI,:,float,=,3.14,;,func,calculateArea,(,radius,:,float,),->,float,{,return,PI,*,radius,*,radius,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_011():
    """Test complex string literal with multiple escape sequences"""
    source = "\"C:\\User\\Doc\\file.hlang\\tNew line after tab\\\"Final quote\""
    expected = "Illegal Escape In String: C:\\U"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_012():
    """Test mixed operators and separators in a single line"""
    source = "((a + b) * c) && (d <= e) >> func_call()"
    expected = "(,(,a,+,b,),*,c,),&&,(,d,<=,e,),>>,func_call,(,),EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_013():
    """Test if-else if-else structure"""
    source = "if (x > 10) { print(\"Large\"); } else if (x < 0) { print(\"Negative\"); } else { print(\"Medium\"); }"
    expected = "if,(,x,>,10,),{,print,(,Large,),;,},else,if,(,x,<,0,),{,print,(,Negative,),;,},else,{,print,(,Medium,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_014():
    """Test for loop with in keyword and array literal"""
    source = "for (item in [1, 2, 3]) { print(item); }"
    expected = "for,(,item,in,[,1,,,2,,,3,],),{,print,(,item,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_015():
    """Test function declaration with multiple parameters and return type"""
    source = "func calculate(val1: int, val2: float, val3: bool) -> string { return \"result\"; }"
    expected = "func,calculate,(,val1,:,int,,,val2,:,float,,,val3,:,bool,),->,string,{,return,result,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_016():
    """Test const and let declarations with type annotations"""
    source = "const MAX_SIZE: int = 100;\nlet message: string = \"Hello\";"
    expected = "const,MAX_SIZE,:,int,=,100,;,let,message,:,string,=,Hello,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_017():
    """Test nested block comments with complex content"""
    source = "/* Comment /* inner with special chars { } [ ] ; */ end */ func main()"
    expected = "func,main,(,),EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_018():
    """Test pipeline operator example"""
    source = "getData() >> processData >> formatResult(\"final\")"
    expected = "getData,(,),>>,processData,>>,formatResult,(,final,),EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_019():
    """Test edge cases for identifiers"""
    source = "_variable _123 another_Var_ abc_def"
    expected = "_variable,_123,another_Var_,abc_def,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_020():
    """Test a more complex expression"""
    source = "(x + y * z) / (a - b) == !status && count < 100"
    expected = "(,x,+,y,*,z,),/,(,a,-,b,),==,!,status,&&,count,<,100,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_021():
    """Test mixed comments (single and multi-line) and code"""
    source = "let x = 1; /* Block comment */ // Line comment\n let y = 2;"
    expected = "let,x,=,1,;,let,y,=,2,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_022():
    """Test floating-point numbers with and without leading zeros, and exponential notation variations"""
    source = "0.5 .5 1.0e+2 2e-3 123.456"
    expected = "0.5,.,5,1.0e+2,2,e,-,3,123.456,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_023():
    """Test boolean and void literals/keywords in expressions"""
    source = "if (true && !false) { func display(): void { return; } }"
    expected = "if,(,true,&&,!,false,),{,func,display,(,),:,void,{,return,;,},},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_024():
    """Test operators that are also part of other tokens (e.g., `=`, `==`)"""
    source = "a = b; if (x == y) { print(\"equal\"); }"
    expected = "a,=,b,;,if,(,x,==,y,),{,print,(,equal,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_025():
    """Test array access with multi-dimensional arrays"""
    source = "matrix[row][col] = value; arr[0];"
    expected = "matrix,[,row,],[,col,],=,value,;,arr,[,0,],;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_026():
    """Test break and continue inside a loop context"""
    source = "while (true) { if (condition) break; else continue; }"
    expected = "while,(,true,),{,if,(,condition,),break,;,else,continue,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_027():
    """Test return statement with different types of expressions"""
    source = "return x + 5; return \"hello\"; return func_call(); return;"
    expected = "return,x,+,5,;,return,hello,;,return,func_call,(,),;,return,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_028():
    """Test function call within an expression"""
    source = "let result = calculate(arg1, arg2) + 10;"
    expected = "let,result,=,calculate,(,arg1,,,arg2,),+,10,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_029():
    """Test a snippet involving multiple variable declarations and assignments"""
    source = "let a: int = 10;\nconst B: float = 20.5;\na = a + 1; B = 21.0; // B should be immutable, this is a syntax test"
    expected = "let,a,:,int,=,10,;,const,B,:,float,=,20.5,;,a,=,a,+,1,;,B,=,21.0,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_030():
    """Test to ensure whitespace is correctly ignored between tokens"""
    source = "  func   main ( )   {   print (  \"Test\"  ) ;   }  "
    expected = "func,main,(,),{,print,(,Test,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_031():
    """Test various integer and float literal representations"""
    source = "0 12345 ********* 0.0 1.2345 5.0e+10 1e-5"
    expected = "0,12345,*********,0.0,1.2345,5.0e+10,1,e,-,5,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_032():
    """Test combinations of different literal types in an expression"""
    source = "let val = 10 + 3.14 * (true || false) && \"text\";"
    expected = "let,val,=,10,+,3.14,*,(,true,||,false,),&&,text,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_033():
    """Test an empty program (only EOF)"""
    source = ""
    expected = "EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_034():
    """Test complex arithmetic expression with parenthesis and modulo"""
    source = "result = ((x + y) * z) % (a / b);"
    expected = "result,=,(,(,x,+,y,),*,z,),%,(,a,/,b,),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_035():
    """Test mixed declaration types (const, let, func)"""
    source = "const APP_NAME: string = \"My App\"; let counter = 0; func init() -> void { print(APP_NAME); }"
    expected = "const,APP_NAME,:,string,=,My App,;,let,counter,=,0,;,func,init,(,),->,void,{,print,(,APP_NAME,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_036():
    """Test array literal with various elements (lexer should tokenize all)"""
    source = "let mixed_array = [1, \"hello\", true, 3.14];"
    expected = "let,mixed_array,=,[,1,,,hello,,,true,,,3.14,],;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_037():
    """Test function call without arguments"""
    source = "myFunction(); anotherCall();"
    expected = "myFunction,(,),;,anotherCall,(,),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_038():
    """Test usage of void keyword for return type"""
    source = "func cleanup() -> void { /* do nothing */ }"
    expected = "func,cleanup,(,),->,void,{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_039():
    """Test variable assignment with expression"""
    source = "total = item_price * quantity; active = status == \"ready\";"
    expected = "total,=,item_price,*,quantity,;,active,=,status,==,ready,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_040():
    """Test a more involved code snippet with nested blocks and control flow"""
    source = """
func process(data: [int; 10]) -> int {
    let sum = 0;
    for (val in data) {
        if (val > 0) {
            sum = sum + val;
        } else {
            break;
        }
    }
    return sum;
}
"""
    expected = "func,process,(,data,:,[,int,;,10,],),->,int,{,let,sum,=,0,;,for,(,val,in,data,),{,if,(,val,>,0,),{,sum,=,sum,+,val,;,},else,{,break,;,},},return,sum,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_041():
    """Test identifier shadowing with let inside a block"""
    source = "let x = 10; { let x = 20; print(x); } print(x);"
    expected = "let,x,=,10,;,{,let,x,=,20,;,print,(,x,),;,},print,(,x,),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_042():
    """Test complex logical expression with multiple &&, ||, ! operators"""
    source = "if (!(a && b) || (c || !d)) { /* code */ }"
    expected = "if,(,!,(,a,&&,b,),||,(,c,||,!,d,),),{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_043():
    """Test function call as an argument to another function call"""
    source = "print(toString(getValue(id)));"
    expected = "print,(,toString,(,getValue,(,id,),),),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_044():
    """Test using built-in functions (print, input, str, int, float, len)"""
    source = "print(input(\"Enter:\")); let l = len(my_array); let num = int(\"123\");"
    expected = "print,(,input,(,Enter:,),),;,let,l,=,len,(,my_array,),;,let,num,=,int,(,123,),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_045():
    """Test array declaration with a specific size"""
    source = "let numbers: [int; 5] = [1, 2, 3, 4, 5];"
    expected = "let,numbers,:,[,int,;,5,],=,[,1,,,2,,,3,,,4,,,5,],;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_046():
    """Test integer and float literals with negative signs"""
    source = "-123 -45.67 -0.001"
    expected = "-,123,-,45.67,-,0.001,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_047():
    """Test comments at the beginning and end of lines, and multi-line comments spanning a single line"""
    source = "// Start comment\nlet x = 10; // End comment\n/* Single line block comment */"
    expected = "let,x,=,10,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_048():
    """Test use of void type in a function signature and in the main function"""
    source = "func doNothing() -> void { /* nothing */ } func main() -> void { doNothing(); }"
    expected = "func,doNothing,(,),->,void,{,},func,main,(,),->,void,{,doNothing,(,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_049():
    """Test combinations of assignment and expression"""
    source = "a = b + c; d = e * (f / g); result = true || false;"
    expected = "a,=,b,+,c,;,d,=,e,*,(,f,/,g,),;,result,=,true,||,false,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_050():
    """Test a basic while loop structure"""
    source = "let i = 0; while (i < 5) { print(i); i = i + 1; }"
    expected = "let,i,=,0,;,while,(,i,<,5,),{,print,(,i,),;,i,=,i,+,1,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_051():
    """Test for loop with an empty array literal"""
    source = "for (item in []) { print(\"Empty\"); }"
    expected = "for,(,item,in,[,],),{,print,(,Empty,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_052():
    """Test string literal containing various ASCII escape sequences"""
    source = "\"New line\\nTab\\tCarriage return\\rBackslash\\\\Quote\\\"\""
    expected = "New line\\nTab\\tCarriage return\\rBackslash\\\\Quote\\\",EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_053():
    """Test multiple chained pipeline operators"""
    source = "initialData >> filterData() >> transformData(param) >> saveData;"
    expected = "initialData,>>,filterData,(,),>>,transformData,(,param,),>>,saveData,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_054():
    """Test complex conditional expression inside an if statement"""
    source = "if (temp > 100 && (pressure < 50 || altitude > 1000)) { alert(); }"
    expected = "if,(,temp,>,100,&&,(,pressure,<,50,||,altitude,>,1000,),),{,alert,(,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_055():
    """Test function declaration without parameters"""
    source = "func greet() -> string { return \"Hello!\"; }"
    expected = "func,greet,(,),->,string,{,return,Hello!,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_056():
    """Test multiple variable declarations on separate lines"""
    source = "let a = 1;\nlet b = 2.0;\nconst C = true;"
    expected = "let,a,=,1,;,let,b,=,2.0,;,const,C,=,true,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_057():
    """Test accessing array elements in a loop"""
    source = "for (i in [0, 1, 2]) { print(myArray[i]); }"
    expected = "for,(,i,in,[,0,,,1,,,2,],),{,print,(,myArray,[,i,],),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_058():
    """Test empty block statement"""
    source = "if (condition) { /* empty block */ } else {}"
    expected = "if,(,condition,),{,},else,{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_059():
    """Test identifier that is a keyword prefix (e.g., forLoop, ifCondition)"""
    source = "let forLoop = 1; let ifCondition = true;"
    expected = "let,forLoop,=,1,;,let,ifCondition,=,true,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_060():
    """Test a simple program demonstrating basic I/O"""
    source = "func main() -> void { let name = input(\"What's your name?\"); print(\"Hello, \" + name); }"
    expected = "func,main,(,),->,void,{,let,name,=,input,(,What's your name?,),;,print,(,Hello, ,+,name,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_061():
    """Test identifier with only underscores"""
    source = "let _ = 1; let __ = 2; let ___abc = 3;"
    expected = "let,_,=,1,;,let,__,=,2,;,let,___abc,=,3,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_062():
    """Test boolean literals in comparison operations"""
    source = "if (active == true && enabled != false) { /* do something */ }"
    expected = "if,(,active,==,true,&&,enabled,!=,false,),{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_063():
    """Test integer literals with leading zeros (should be treated as regular integers)"""
    source = "0123 007 0"
    expected = "0123,007,0,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_064():
    """Test string literal with an escaped double quote and unescaped quote (should end the string)"""
    source = "\"This is a \\\"quoted\\\" string.\" + \"And this ends here\" and_another_token"
    expected = "This is a \\\"quoted\\\" string.,+,And this ends here,and_another_token,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_065():
    """Test function signature with multiple parameters of the same type"""
    source = "func sum(a: int, b: int, c: int) -> int { return a + b + c; }"
    expected = "func,sum,(,a,:,int,,,b,:,int,,,c,:,int,),->,int,{,return,a,+,b,+,c,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_066():
    """Test division and modulo operators"""
    source = "result = (dividend / divisor) % remainder;"
    expected = "result,=,(,dividend,/,divisor,),%,remainder,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_067():
    """Test program with only constant declarations"""
    source = "const PI: float = 3.14; const EULER: float = 2.718;"
    expected = "const,PI,:,float,=,3.14,;,const,EULER,:,float,=,2.718,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_068():
    """Test break statement outside a loop (lexer should still tokenize it)"""
    source = "break; // This would be a parser error, but lexer should find 'break'"
    expected = "break,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_069():
    """Test continue statement at the end of a block"""
    source = "while (x < 10) { print(x); x = x + 1; continue; }"
    expected = "while,(,x,<,10,),{,print,(,x,),;,x,=,x,+,1,;,continue,;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_070():
    """Test the factorial example program from HLang specification"""
    source = """
func factorial(n: int) -> int {
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

func main() -> void {
    let num = 5;
    let result = factorial(num);
    print("Factorial of " + str(num) + " is " + str(result));
}
"""
    expected = "func,factorial,(,n,:,int,),->,int,{,if,(,n,<=,1,),{,return,1,;,},return,n,*,factorial,(,n,-,1,),;,},func,main,(,),->,void,{,let,num,=,5,;,let,result,=,factorial,(,num,),;,print,(,Factorial of ,+,str,(,num,),+, is ,+,str,(,result,),),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected
    
def test_071():
    """Test array literal with nested array (lexer should tokenize all tokens)"""
    source = "let nested_arr = [[1, 2], [3, 4]];"
    expected = "let,nested_arr,=,[,[,1,,,2,],,,[,3,,,4,],],;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_072():
    """Test complex for loop with continue and break inside"""
    source = "for (i in [1,2,3,4,5]) { if (i == 3) continue; if (i == 5) break; print(i); }"
    expected = "for,(,i,in,[,1,,,2,,,3,,,4,,,5,],),{,if,(,i,==,3,),continue,;,if,(,i,==,5,),break,;,print,(,i,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_073():
    """Test string concatenation with multiple + operators"""
    source = "let msg = \"Hello\" + \" \" + \"World\" + \"!\";"
    expected = "let,msg,=,Hello,+, ,+,World,+,!,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_074():
    """Test identifier containing digits and underscores (after first char/underscore)"""
    source = "let var_123_abc = 42;"
    expected = "let,var_123_abc,=,42,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_075():
    """Test float literals with scientific notation, including negative exponent"""
    source = "1.23E+5 4.56e-2 7.0e0"
    expected = "1.23E+5,4.56e-2,7.0e0,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_076():
    """Test string literal with explicit newline escape sequence"""
    source = "\"First line\\nSecond line\""
    expected = "First line\\nSecond line,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_077():
    """Test declaration of void type for a variable (lexer should tokenize, parser would flag)"""
    source = "let empty_val: void = /* nothing */; // Syntactically incorrect, but lexer's job is tokenization"
    expected = "let,empty_val,:,void,=,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_078():
    """Test complex boolean expression combining multiple operators and parentheses"""
    source = "if ((a > b && c <= d) || !(e == f)) { /* action */ }"
    expected = "if,(,(,a,>,b,&&,c,<=,d,),||,!,(,e,==,f,),),{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_079():
    """Test functions returning arrays (lexer should tokenize type correctly)"""
    source = "func getNumbers() -> [int; 3] { return [1, 2, 3]; }"
    expected = "func,getNumbers,(,),->,[,int,;,3,],{,return,[,1,,,2,,,3,],;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_080():
    """Test Array Processing example from HLang spec"""
    source = """
func sum_array(arr: [int; 5]) -> int {
    let total = 0;
    for (element in arr) {
        total = total + element;
    }
    return total;
}

func main() -> void {
    let numbers: [int; 5] = [1, 2, 3, 4, 5];
    let sum = sum_array(numbers);
    print("Sum of array: " + str(sum));
}
"""
    expected = "func,sum_array,(,arr,:,[,int,;,5,],),->,int,{,let,total,=,0,;,for,(,element,in,arr,),{,total,=,total,+,element,;,},return,total,;,},func,main,(,),->,void,{,let,numbers,:,[,int,;,5,],=,[,1,,,2,,,3,,,4,,,5,],;,let,sum,=,sum_array,(,numbers,),;,print,(,Sum of array: ,+,str,(,sum,),),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_081():
    """Test Simple Calculator example from HLang spec"""
    source = """
func add(a: float, b: float) -> float {
    return a + b;
}

func multiply(a: float, b: float) -> float {
    return a * b;
}

func main() -> void {
    let x = 10.5;
    let y = 3.2;
    
    print("Addition: " + str(add(x, y)));
    print("Multiplication: " + str(multiply(x, y)));
}
"""
    expected = "func,add,(,a,:,float,,,b,:,float,),->,float,{,return,a,+,b,;,},func,multiply,(,a,:,float,,,b,:,float,),->,float,{,return,a,*,b,;,},func,main,(,),->,void,{,let,x,=,10.5,;,let,y,=,3.2,;,print,(,Addition: ,+,str,(,add,(,x,,,y,),),),;,print,(,Multiplication: ,+,str,(,multiply,(,x,,,y,),),),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_082():
    """Test use of input and print with complex expressions"""
    source = "print(\"Result: \" + str(int(input(\"Enter num:\")) * 2));"
    expected = "print,(,Result: ,+,str,(,int,(,input,(,Enter num:,),),*,2,),),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_083():
    """Test unary negation for both int and float"""
    source = "let neg_int = -10; let neg_float = -3.14;"
    expected = "let,neg_int,=,-,10,;,let,neg_float,=,-,3.14,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_084():
    """Test chained comparison operators (lexer parses individually)"""
    source = "if (a < b == c > d) { /* invalid syntax for parser, valid for lexer */ }"
    expected = "if,(,a,<,b,==,c,>,d,),{,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_085():
    """Test function call with a literal as an argument"""
    source = "process(\"data\"); calculate(100);"
    expected = "process,(,data,),;,calculate,(,100,),;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_086():
    """Test block comments with leading/trailing spaces on the line"""
    source = "/* This is a block comment.   */ let x = 1;"
    expected = "let,x,=,1,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_087():
    """Test a program fragment with only an expression statement"""
    source = "a + b * c;"
    expected = "a,+,b,*,c,;,EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected

def test_088():
    """Test a mix of const, let, and function calls in a single snippet"""
    source = """
const GLOBAL_LIMIT: int = 50;
func check(val: int) -> bool {
    let result = val < GLOBAL_LIMIT;
    return result;
}
let current_value = 40;
if (check(current_value)) {
    print("Within limit");
}
"""
    expected = "const,GLOBAL_LIMIT,:,int,=,50,;,func,check,(,val,:,int,),->,bool,{,let,result,=,val,<,GLOBAL_LIMIT,;,return,result,;,},let,current_value,=,40,;,if,(,check,(,current_value,),),{,print,(,Within limit,),;,},EOF"
    assert Tokenizer(source).get_tokens_as_string() == expected