from utils import <PERSON><PERSON><PERSON>


def test_001():
    """Test basic function declaration"""
    source = """func main() -> void {}"""
    expected = "success"
    assert Parse<PERSON>(source).parse() == expected


def test_002():
    """Test function with parameters"""
    source = """func add(a: int, b: int) -> int { return a + b; }"""
    expected = "success"
    assert Parse<PERSON>(source).parse() == expected


def test_003():
    """Test variable declaration with type annotation"""
    source = """func main() -> void { let x: int = 42; }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_004():
    """Test variable declaration with type inference"""
    source = """func main() -> void { let name = "Alice"; }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_005():
    """Test constant declaration"""
    source = """const PI: float = 3.14159; func main() -> void {}"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_006():
    """Test if-else statement"""
    source = """func main() -> void { 
        if (x > 0) { 
            print("positive"); 
        } else { 
            print("negative"); 
        }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_007():
    """Test while loop"""
    source = """func main() -> void { 
        let i = 0;
        while (i < 10) { 
            i = i + 1; 
        }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_008():
    """Test for loop with array"""
    source = """func main() -> void { 
        let numbers = [1, 2, 3, 4, 5];
        for (num in numbers) { 
            print(str(num)); 
        }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_009():
    """Test array declaration and access"""
    source = """func main() -> void { 
        let arr: [int; 3] = [1, 2, 3];
        let first = arr[0];
        arr[1] = 42;
    }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_010():
    """Test complex expression with pipeline operator"""
    source = """func main() -> void { 
        let result = data >> process >> validate >> transform;
        let calculation = 5 >> add(3) >> multiply(2);
    }"""
    expected = "success"
    assert Parser(source).parse() == expected


def test_011():
    """Test parser error: missing closing brace in function declaration"""
    source = """func main() -> void { let x = 1; """  # Thiếu dấu }
    expected = "Error on line 1 col 33: <EOF>"
    assert Parser(source).parse() == expected


def test_012():
    """Test while loop with break and continue"""
    source = """func main() -> void {
        let i = 0; 
        while (i < 10) { 
            if (i == 5) {break;} 
            if (i == 2) { 
                i = i + 1; 
                continue; 
            } 
            print(i); 
            i = i + 1; 
        }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_013():
    """Test function definition with an empty body"""
    source = """func doNothing() -> void {}"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_014():
    """Test array access and assignment"""
    source = """
    func main() -> void {
        let arr: [int; 2] = [1, 2]; 
        arr[0] = 5; 
        let val = arr[1];
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_015():
    """Test pipeline operator usage"""
    source = """
    func main() -> void {
        let x = 10; 
        x >> add(5) >> multiply(2);
        let result = 5 >> add(3) >> multiply(2);
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_016():
    """Test string literal with escape sequences"""
    source = """
    func main() -> void {
        print("Hello\\nWorld!\\tTabbed text.");
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_017():
    """Test missing type annotation in a variable declaration"""
    source = """
    func main() -> void {
        let x = 10;
    }""" 
    expected = "success"
    assert Parser(source).parse() == expected

def test_018():
    """Test parser error: missing expression after operator"""
    source = """
    func Test() -> void {
        let result = 5 + ;
    }"""  # Thiếu biểu thức sau +
    expected = "Error on line 3 col 25: ;"
    assert Parser(source).parse() == expected

def test_019():
    """Test parser error: incorrect return type syntax for a function"""
    source = """func calculate(a: int) -> { return a; }"""  # Thiếu kiểu trả về
    expected = "Error on line 1 col 26: {"
    assert Parser(source).parse() == expected

def test_020():
    """Test parser error: const in function"""
    source = """
    func test() -> void {
        const C = 10; C = 20;
    }"""  
    expected = "Error on line 3 col 8: const"
    assert Parser(source).parse() == expected

def test_021():
    """Test function with multiple return statements (only one reachable path)"""
    source = """func check(x: int) -> int { if (x > 0) { return 1; } return 0; }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_022():
    """Test block statement with multiple statements"""
    source = """
    func main() -> void {
    { let x = 1; print(x); let y = 2; print(y); }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_023():
    """Test array literal with various numeric types (lexer/parser tokenize, type check later)"""
    source = """
    func main() -> void {
        let mixed_numbers = [1, 2.5, 3];
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_024():
    """Test empty main function"""
    source = """func main() -> void {}"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_025():
    """Test a program with constant declarations"""
    source = """const PI: float = 3.14; const MAX_COUNT: int = 100;
    func main() -> void {}"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_026():
    """Test parser error: Function declaration without a return type (missing -> and type)"""
    source = """func greet() { print("Hello"); }""" # Thiếu -> type
    expected = "Error on line 1 col 13: {"
    assert Parser(source).parse() == expected

def test_027():
    """Test parser error: if statement missing its condition in parentheses"""
    source = """
    func main() -> void {
    if x > 0 { print("Positive"); }
    }""" # Thiếu dấu ngoặc đơn quanh điều kiện
    expected = "Error on line 3 col 7: x"
    assert Parser(source).parse() == expected

def test_028():
    """Test parser error: for loop missing 'in' keyword"""
    source = """
    func main() -> void {
        for (item [1, 2, 3]) { print(item); }
    }""" # Thiếu 'in'
    expected = "Error on line 3 col 18: ["
    assert Parser(source).parse() == expected

def test_029():
    """Test parser error: Invalid character in identifier (e.g., 'my-var')"""
    source = """
    func main() -> void {
    let my-var = 10;
    }""" # Dấu '-' không hợp lệ trong định danh
    expected = "Error on line 3 col 10: -"
    assert Parser(source).parse() == expected

def test_030():
    """Test parser: Missing main function"""
    source = """func otherFunc() -> void {}""" # Không có hàm main
    expected = "success" 
    assert Parser(source).parse() == expected

def test_031():
    """Test multiple constant and variable declarations mixed"""
    source = """const VERSION: string = "1.0"; let counter: int = 0; const MAX_ATTEMPTS: int = 3;"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_032():
    """Test program with a single print statement inside main"""
    source = """func main() -> void { print("Hello HLang!"); }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_033():
    """Test nested blocks with variable declarations"""
    source = """{ let x = 1; { let y = 2; print(x + y); } print(x); }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_034():
    """Test function calls with no arguments"""
    source = """func init() -> void {} func run() -> void { init(); }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_035():
    """Test complex arithmetic expression as an assignment value"""
    source = """let result = (10 + 5) * 2 / (7 - 4);"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_036():
    """Test parser error: Missing colon for type annotation"""
    source = """let x int = 10;"""  # Thiếu dấu :
    expected = "Error on line 1 col 7: 'int'"
    assert Parser(source).parse() == expected

def test_037():
    """Test parser error: Incorrect usage of return outside a function"""
    source = """return 1;"""  # 'return' nằm ngoài hàm
    expected = "Error on line 1 col 1: 'return'"
    assert Parser(source).parse() == expected

def test_038():
    """Test parser error: Missing condition for while loop"""
    source = """while () { print("Loop"); }"""  # Thiếu điều kiện trong ngoặc đơn
    expected = "Error on line 1 col 8: ')'"
    assert Parser(source).parse() == expected

def test_039():
    """Test parser error: Redundant semicolon (e.g., ;;)"""
    source = """let x = 10;;"""  # Dấu ;; không hợp lệ
    expected = "Error on line 1 col 11: ';'"
    assert Parser(source).parse() == expected

def test_040():
    """Test parser error: Keyword used as an identifier"""
    source = """let if = 10;"""  # 'if' là từ khóa không thể dùng làm định danh
    expected = "Error on line 1 col 5: 'if'"
    assert Parser(source).parse() == expected

def test_089():
    """Test nested if-else if-else structure"""
    source = """
    func FUN() -> void {
    if (a > 0) { if (b < 0) { print("Negative b"); } else { print("Positive b"); } } else if (a == 0) { print("Zero a"); } else { print("Negative a"); }
    }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_090():
    """Use of composite array and indexing in return expressions"""
    source = """func get2D() -> [[int; 2]; 2] {
        return [[10, 20], [30, 40]];
    }
    func main() -> void {
        let x = get2D()[1][0];
    } """
    expected = "success"
    assert Parser(source).parse() == expected

def test_091():
    """Test basic function declaration"""
    source = """func main() -> void {}"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_092():
    """Test function with parameters and return type, with a simple return statement"""
    source = """func add(a: int, b: int) -> int { return a + b; }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_093():
    """Test variable declaration and assignment with different types"""
    source = """let x: int = 10; const PI: float = 3.14; let name: string = "HLang";"""
    expected = "Error on line 1 col 0: let"
    assert Parser(source).parse() == expected

def test_094():
    """Test simple if-else statement"""
    source = """
func printer() -> void { 
    if (x > 5) { print("Greater"); } else { print("Smaller"); }
}
"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_095():
    """Test for loop with array literal and a print statement"""
    source = """
    func testfor() -> void {
        for (item in [1, 2, 3]) { print(item); }
    }
    """
    expected = "success"
    assert Parser(source).parse() == expected

def test_096():
    """Test a combination of constant declaration, function call, and arithmetic expression"""
    source = """const MAX = 100; func calculate(a: int) -> int { return a * MAX; }"""
    expected = "success"
    assert Parser(source).parse() == expected

def test_097():
    """Test parser error: missing closing parenthesis in a function call"""
    source = """
    func fun() -> void {
    print("Hello";
    }
    """  # Thiếu dấu )
    expected = "Error on line 3 col 17: ;"
    assert Parser(source).parse() == expected

def test_098():
    """Test parser error: missing semicolon at the end of a statement"""
    source = """func fun() -> void {
    let x = 10 let y = 20;
    }
    """  # Thiếu dấu ; sau 10
    expected = "Error on line 2 col 15: let"
    assert Parser(source).parse() == expected

def test_099():
    """Test parser error: incorrect keyword usage (e.g., 'fun' instead of 'func')"""
    source = """fun main() -> void {}"""  # Dùng 'fun' thay vì 'func'
    expected = "Error on line 1 col 0: fun"
    assert Parser(source).parse() == expected

def test_100():
    """Test parser error: mismatched braces or brackets (missing closing brace)"""
    source = """func test() -> int { return 10; """  # Thiếu dấu }
    expected = "Error on line 1 col 32: <EOF>"
    assert Parser(source).parse() == expected