BOOL=1
BREAK=2
CONST=3
CONTINUE=4
ELSE=5
FLOAT=6
FOR=7
FUNC=8
IF=9
IN=10
INT=11
LET=12
RETURN=13
STRING=14
VOID=15
WHILE=16
FLOAT_LIT=17
INT_LIT=18
BOOLEAN_LIT=19
STRING_LIT=20
COLON=21
RETURN_TYPE=22
PIPELINE=23
EQ=24
DIFF=25
LTE=26
LT=27
GTE=28
GT=29
AND=30
OR=31
NOT=32
ASTERISK=33
ASSIGN=34
ADD=35
SUB=36
MUL=37
DIV=38
MOD=39
LP=40
RP=41
LBRACE=42
RBRACE=43
LSB=44
RSB=45
COMMA=46
SMCOLON=47
DOT=48
ID=49
WS=50
SINGLE_LINE_COMMENT=51
MULTI_LINE_COMMENT=52
ILLEGAL_ESCAPE=53
UNCLOSE_STRING=54
ERROR_CHAR=55
'bool'=1
'break'=2
'const'=3
'continue'=4
'else'=5
'float'=6
'for'=7
'func'=8
'if'=9
'in'=10
'int'=11
'let'=12
'return'=13
'string'=14
'void'=15
'while'=16
':'=21
'->'=22
'>>'=23
'=='=24
'!='=25
'<='=26
'<'=27
'>='=28
'>'=29
'&&'=30
'||'=31
'!'=32
'*/'=33
'='=34
'+'=35
'-'=36
'*'=37
'/'=38
'%'=39
'('=40
')'=41
'{'=42
'}'=43
'['=44
']'=45
','=46
';'=47
'.'=48
