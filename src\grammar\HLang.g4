grammar HLang;

@lexer::header {
from lexererr import *
}

@lexer::members {
def __init__(self, input=None, output:TextIO = sys.stdout):
    super().__init__(input, output)
    self.checkVersion("4.13.2")
    self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
    self._actions = None
    self._predicates = None
    self.preType = None


def emit(self):
    tk = self.type
    self.preType = tk
    if tk == self.UNCLOSE_STRING:       
        result = super().emit();
        raise UncloseString(result.text);
    elif tk == self.ILLEGAL_ESCAPE:
        result = super().emit();
        raise IllegalEscape(result.text);
    elif tk == self.ERROR_CHAR:
        result = super().emit();
        raise ErrorToken(result.text); 
    else:
        return super().emit();
}

options{
	language=Python3;
}

program: const_decllist func_decllist EOF; // write for program rule here using vardecl and funcdecl

func_decllist: func_decl func_decllist | func_decl;
func_decl: FUNC ID LP param_list RP RETURN_TYPE (argtype | VOID) LBRACE stmt_list RBRACE;

param_list: param_decllist | ;
param_decllist: param_decl COMMA param_decllist | param_decl;
param_decl: ID COLON argtype;

stmt
    : expr_stmt
    | var_decl
    | assign_stmt
    | if_stmt
    | while_stmt
    | for_stmt
    | break_stmt
    | cont_stmt
    | return_stmt
    | block_stmt
    ;

expr_stmt: expr SMCOLON;

assign_stmt: valid_lvalue ASSIGN expr SMCOLON;

valid_lvalue
    : ID
    | expr array_access
    ;

if_stmt: IF LP expr RP block_stmt elseif_part else_part ;

elseif_part: elseif_list | ;
elseif_list: elseif elseif_list | elseif ;
elseif: ELSE IF LP expr RP block_stmt;

else_part: ELSE block_stmt | ;

while_stmt: WHILE LP expr RP block_stmt;

for_stmt: FOR LP ID IN expr RP block_stmt;

break_stmt: BREAK SMCOLON;

cont_stmt: CONTINUE SMCOLON;

return_stmt: RETURN (expr | ) SMCOLON;

block_stmt: LBRACE stmt_list RBRACE;
stmt_list: stmt stmt_list | ;

expr: expr PIPELINE expr1 | expr1;
expr1: expr1 OR expr2 | expr2;
expr2: expr2 AND expr3 | expr3;
expr3: expr3 (EQ | DIFF) expr4 | expr4;
expr4: expr4 (LTE | LT | GTE | GT) expr5 | expr5;
expr5: expr5 (ADD | SUB) expr6 | expr6;
expr6: expr6 (MUL | DIV | MOD) expr7 | expr7;
expr7: (NOT | SUB | ADD) expr7 | expr8;
expr8: expr8 array_access
    | expr8 LP arg_list RP
    | expr9;
expr9: LP expr RP
    | INT_LIT
    | FLOAT_LIT
    | STRING_LIT
    | BOOLEAN_LIT
    | array_lit
    | ID;

array_access: LSB expr RSB;

arg_list: arg_prime | ;
arg_prime: expr COMMA arg_prime | expr;


var_decl: LET ID (COLON (argtype | functype))? ASSIGN expr SMCOLON;

const_decllist: const_decl const_decllist | ;
const_decl: CONST ID (COLON (argtype | functype))? ASSIGN expr SMCOLON;

arrtype: LSB arrtype SMCOLON INT_LIT RSB | arrtype_prime ;
arrtype_prime: LSB (var_primetype | functype) SMCOLON INT_LIT RSB;

functype: LP functype_elememnts RP RETURN_TYPE (argtype | VOID);
functype_elememnts: argtypelist | ;

argtypelist: argtype COMMA argtypelist | argtype;
argtype: var_primetype | arrtype;

var_primetype: INT | FLOAT | BOOL | STRING;


array_lit: LSB (list_element_prime | ) RSB;

list_element_prime
    : expr COMMA list_element_prime
    | expr
    ;


// Keywords
BOOL     : 'bool'     ;
BREAK    : 'break'    ;
CONST    : 'const'    ;
CONTINUE : 'continue' ;
ELSE     : 'else'     ;
FLOAT    : 'float'    ;
FOR      : 'for'      ;
FUNC     : 'func'     ;
IF       : 'if'       ;
IN       : 'in'       ;
INT      : 'int'      ;
LET      : 'let'      ;
RETURN   : 'return'   ;
STRING   : 'string'   ;
VOID     : 'void'     ;
WHILE    : 'while'    ;

// Float Literals
fragment INTEGER_PART   : [0-9]+; 
fragment FRACTIONAL_PART: '.' [0-9]*; 
fragment EXPONENT_PART  : [Ee] [+-]? [0-9]+; 
FLOAT_LIT               : INTEGER_PART FRACTIONAL_PART (EXPONENT_PART)?;

INT_LIT: [0-9]+;

fragment TRUE   : 'true';
fragment FALSE  : 'false';
BOOLEAN_LIT     : TRUE | FALSE;

fragment STRINGPRIME: ('\u0000'..'\u0008' 
                    | '\u000B'..'\u000C' 
                    | '\u000E'..'\u0021' 
                    | '\u0023'..'\u005B' 
                    | '\u005D'..'\u007F' 
                    | '\\' [ntr"\\]);
STRING_LIT          : '"' STRINGPRIME* '"' 
{ 
    self.text = self.text[1:-1] 
};

// ARRAY_LIT   : LSB WS* 
//             (INT_LIT (WS* COMMA WS* INT_LIT)*
//             | FLOAT_LIT (WS* COMMA WS* FLOAT_LIT)*
//             | STRING_LIT (WS* COMMA WS* STRING_LIT)*
//             | BOOLEAN_LIT (WS* COMMA WS* BOOLEAN_LIT)*
//             )?
//               WS* RSB;

COLON       : ':';
RETURN_TYPE : '->';
PIPELINE    : '>>';

EQ  : '==';
DIFF: '!=';
LTE : '<=';
LT  : '<';
GTE : '>=';
GT  : '>';
AND : '&&'; 
OR  : '||';   
NOT : '!';

ASTERISK: '*/'{ self.type = self.ERROR_CHAR};

ASSIGN  : '=';
ADD     : '+';
SUB     : '-';
MUL     : '*';
DIV     : '/';
MOD     : '%';

LP      : '('; // Left Parenthesis
RP      : ')'; // Right Parenthesis
LBRACE  : '{'; 
RBRACE  : '}';
LSB     : '['; // Left Square Bracket
RSB     : ']'; // Right Square Bracket
COMMA   : ','; // Comma
SMCOLON : ';';
DOT     : '.';

ID: [a-zA-Z_][a-zA-Z0-9_]*;

WS                  : [ \t\r\n]+ -> skip ; // skip spaces, tabs 
SINGLE_LINE_COMMENT : '//' ~[\r\n]* -> skip;
MULTI_LINE_COMMENT  : '/*' (MULTI_LINE_COMMENT | .)*? '*/' {
counter = 0
i = 0
n = len(self.text)

while i < n:
    if (i + 1) < n and self.text[i:i+2] == '/*':
        counter += 1
        i += 2
    elif (i + 1) < n and self.text[i:i+2] == '*/':
        counter -= 1
        if counter < 0:
            break
        i += 2
    else:
        i += 1
if counter == 0:
    self.skip()
else:
    self.type = self.ERROR_CHAR
};

ILLEGAL_ESCAPE: '"' STRINGPRIME* '\\' ~[ntr"\\] { self.text = self.text[1:] };
UNCLOSE_STRING: '"' STRINGPRIME* (EOF | '\n' | '\r\n') { self.text = self.text[1:] };
ERROR_CHAR: . ;