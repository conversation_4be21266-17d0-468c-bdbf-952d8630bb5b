# Generated from c:/Users/<USER>/Documents/HK242/PPL/BTL_New/hlang-compiler/src/grammar/HLang.g4 by ANTLR 4.13.1
from antlr4 import *
from io import StringIO
import sys
if sys.version_info[1] > 5:
    from typing import TextIO
else:
    from typing.io import TextIO


from lexererr import *


def serializedATN():
    return [
        4,0,55,403,6,-1,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,
        2,6,7,6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,
        13,7,13,2,14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,
        19,2,20,7,20,2,21,7,21,2,22,7,22,2,23,7,23,2,24,7,24,2,25,7,25,2,
        26,7,26,2,27,7,27,2,28,7,28,2,29,7,29,2,30,7,30,2,31,7,31,2,32,7,
        32,2,33,7,33,2,34,7,34,2,35,7,35,2,36,7,36,2,37,7,37,2,38,7,38,2,
        39,7,39,2,40,7,40,2,41,7,41,2,42,7,42,2,43,7,43,2,44,7,44,2,45,7,
        45,2,46,7,46,2,47,7,47,2,48,7,48,2,49,7,49,2,50,7,50,2,51,7,51,2,
        52,7,52,2,53,7,53,2,54,7,54,2,55,7,55,2,56,7,56,2,57,7,57,2,58,7,
        58,2,59,7,59,2,60,7,60,1,0,1,0,1,0,1,0,1,0,1,1,1,1,1,1,1,1,1,1,1,
        1,1,2,1,2,1,2,1,2,1,2,1,2,1,3,1,3,1,3,1,3,1,3,1,3,1,3,1,3,1,3,1,
        4,1,4,1,4,1,4,1,4,1,5,1,5,1,5,1,5,1,5,1,5,1,6,1,6,1,6,1,6,1,7,1,
        7,1,7,1,7,1,7,1,8,1,8,1,8,1,9,1,9,1,9,1,10,1,10,1,10,1,10,1,11,1,
        11,1,11,1,11,1,12,1,12,1,12,1,12,1,12,1,12,1,12,1,13,1,13,1,13,1,
        13,1,13,1,13,1,13,1,14,1,14,1,14,1,14,1,14,1,15,1,15,1,15,1,15,1,
        15,1,15,1,16,4,16,210,8,16,11,16,12,16,211,1,17,1,17,5,17,216,8,
        17,10,17,12,17,219,9,17,1,18,1,18,3,18,223,8,18,1,18,4,18,226,8,
        18,11,18,12,18,227,1,19,1,19,1,19,3,19,233,8,19,1,20,4,20,236,8,
        20,11,20,12,20,237,1,21,1,21,1,21,1,21,1,21,1,22,1,22,1,22,1,22,
        1,22,1,22,1,23,1,23,3,23,253,8,23,1,24,1,24,1,24,3,24,258,8,24,1,
        25,1,25,5,25,262,8,25,10,25,12,25,265,9,25,1,25,1,25,1,25,1,26,1,
        26,1,27,1,27,1,27,1,28,1,28,1,28,1,29,1,29,1,29,1,30,1,30,1,30,1,
        31,1,31,1,31,1,32,1,32,1,33,1,33,1,33,1,34,1,34,1,35,1,35,1,35,1,
        36,1,36,1,36,1,37,1,37,1,38,1,38,1,38,1,38,1,38,1,39,1,39,1,40,1,
        40,1,41,1,41,1,42,1,42,1,43,1,43,1,44,1,44,1,45,1,45,1,46,1,46,1,
        47,1,47,1,48,1,48,1,49,1,49,1,50,1,50,1,51,1,51,1,52,1,52,1,53,1,
        53,1,54,1,54,5,54,339,8,54,10,54,12,54,342,9,54,1,55,4,55,345,8,
        55,11,55,12,55,346,1,55,1,55,1,56,1,56,1,56,1,56,5,56,355,8,56,10,
        56,12,56,358,9,56,1,56,1,56,1,57,1,57,1,57,1,57,1,57,5,57,367,8,
        57,10,57,12,57,370,9,57,1,57,1,57,1,57,1,57,1,57,1,58,1,58,5,58,
        379,8,58,10,58,12,58,382,9,58,1,58,1,58,1,58,1,58,1,59,1,59,5,59,
        390,8,59,10,59,12,59,393,9,59,1,59,1,59,1,59,3,59,398,8,59,1,59,
        1,59,1,60,1,60,1,368,0,61,1,1,3,2,5,3,7,4,9,5,11,6,13,7,15,8,17,
        9,19,10,21,11,23,12,25,13,27,14,29,15,31,16,33,0,35,0,37,0,39,17,
        41,18,43,0,45,0,47,19,49,0,51,20,53,21,55,22,57,23,59,24,61,25,63,
        26,65,27,67,28,69,29,71,30,73,31,75,32,77,33,79,34,81,35,83,36,85,
        37,87,38,89,39,91,40,93,41,95,42,97,43,99,44,101,45,103,46,105,47,
        107,48,109,49,111,50,113,51,115,52,117,53,119,54,121,55,1,0,10,1,
        0,48,57,2,0,69,69,101,101,2,0,43,43,45,45,5,0,0,8,11,12,14,33,35,
        91,93,127,5,0,34,34,92,92,110,110,114,114,116,116,3,0,65,90,95,95,
        97,122,4,0,48,57,65,90,95,95,97,122,3,0,9,10,13,13,32,32,2,0,10,
        10,13,13,1,1,10,10,413,0,1,1,0,0,0,0,3,1,0,0,0,0,5,1,0,0,0,0,7,1,
        0,0,0,0,9,1,0,0,0,0,11,1,0,0,0,0,13,1,0,0,0,0,15,1,0,0,0,0,17,1,
        0,0,0,0,19,1,0,0,0,0,21,1,0,0,0,0,23,1,0,0,0,0,25,1,0,0,0,0,27,1,
        0,0,0,0,29,1,0,0,0,0,31,1,0,0,0,0,39,1,0,0,0,0,41,1,0,0,0,0,47,1,
        0,0,0,0,51,1,0,0,0,0,53,1,0,0,0,0,55,1,0,0,0,0,57,1,0,0,0,0,59,1,
        0,0,0,0,61,1,0,0,0,0,63,1,0,0,0,0,65,1,0,0,0,0,67,1,0,0,0,0,69,1,
        0,0,0,0,71,1,0,0,0,0,73,1,0,0,0,0,75,1,0,0,0,0,77,1,0,0,0,0,79,1,
        0,0,0,0,81,1,0,0,0,0,83,1,0,0,0,0,85,1,0,0,0,0,87,1,0,0,0,0,89,1,
        0,0,0,0,91,1,0,0,0,0,93,1,0,0,0,0,95,1,0,0,0,0,97,1,0,0,0,0,99,1,
        0,0,0,0,101,1,0,0,0,0,103,1,0,0,0,0,105,1,0,0,0,0,107,1,0,0,0,0,
        109,1,0,0,0,0,111,1,0,0,0,0,113,1,0,0,0,0,115,1,0,0,0,0,117,1,0,
        0,0,0,119,1,0,0,0,0,121,1,0,0,0,1,123,1,0,0,0,3,128,1,0,0,0,5,134,
        1,0,0,0,7,140,1,0,0,0,9,149,1,0,0,0,11,154,1,0,0,0,13,160,1,0,0,
        0,15,164,1,0,0,0,17,169,1,0,0,0,19,172,1,0,0,0,21,175,1,0,0,0,23,
        179,1,0,0,0,25,183,1,0,0,0,27,190,1,0,0,0,29,197,1,0,0,0,31,202,
        1,0,0,0,33,209,1,0,0,0,35,213,1,0,0,0,37,220,1,0,0,0,39,229,1,0,
        0,0,41,235,1,0,0,0,43,239,1,0,0,0,45,244,1,0,0,0,47,252,1,0,0,0,
        49,257,1,0,0,0,51,259,1,0,0,0,53,269,1,0,0,0,55,271,1,0,0,0,57,274,
        1,0,0,0,59,277,1,0,0,0,61,280,1,0,0,0,63,283,1,0,0,0,65,286,1,0,
        0,0,67,288,1,0,0,0,69,291,1,0,0,0,71,293,1,0,0,0,73,296,1,0,0,0,
        75,299,1,0,0,0,77,301,1,0,0,0,79,306,1,0,0,0,81,308,1,0,0,0,83,310,
        1,0,0,0,85,312,1,0,0,0,87,314,1,0,0,0,89,316,1,0,0,0,91,318,1,0,
        0,0,93,320,1,0,0,0,95,322,1,0,0,0,97,324,1,0,0,0,99,326,1,0,0,0,
        101,328,1,0,0,0,103,330,1,0,0,0,105,332,1,0,0,0,107,334,1,0,0,0,
        109,336,1,0,0,0,111,344,1,0,0,0,113,350,1,0,0,0,115,361,1,0,0,0,
        117,376,1,0,0,0,119,387,1,0,0,0,121,401,1,0,0,0,123,124,5,98,0,0,
        124,125,5,111,0,0,125,126,5,111,0,0,126,127,5,108,0,0,127,2,1,0,
        0,0,128,129,5,98,0,0,129,130,5,114,0,0,130,131,5,101,0,0,131,132,
        5,97,0,0,132,133,5,107,0,0,133,4,1,0,0,0,134,135,5,99,0,0,135,136,
        5,111,0,0,136,137,5,110,0,0,137,138,5,115,0,0,138,139,5,116,0,0,
        139,6,1,0,0,0,140,141,5,99,0,0,141,142,5,111,0,0,142,143,5,110,0,
        0,143,144,5,116,0,0,144,145,5,105,0,0,145,146,5,110,0,0,146,147,
        5,117,0,0,147,148,5,101,0,0,148,8,1,0,0,0,149,150,5,101,0,0,150,
        151,5,108,0,0,151,152,5,115,0,0,152,153,5,101,0,0,153,10,1,0,0,0,
        154,155,5,102,0,0,155,156,5,108,0,0,156,157,5,111,0,0,157,158,5,
        97,0,0,158,159,5,116,0,0,159,12,1,0,0,0,160,161,5,102,0,0,161,162,
        5,111,0,0,162,163,5,114,0,0,163,14,1,0,0,0,164,165,5,102,0,0,165,
        166,5,117,0,0,166,167,5,110,0,0,167,168,5,99,0,0,168,16,1,0,0,0,
        169,170,5,105,0,0,170,171,5,102,0,0,171,18,1,0,0,0,172,173,5,105,
        0,0,173,174,5,110,0,0,174,20,1,0,0,0,175,176,5,105,0,0,176,177,5,
        110,0,0,177,178,5,116,0,0,178,22,1,0,0,0,179,180,5,108,0,0,180,181,
        5,101,0,0,181,182,5,116,0,0,182,24,1,0,0,0,183,184,5,114,0,0,184,
        185,5,101,0,0,185,186,5,116,0,0,186,187,5,117,0,0,187,188,5,114,
        0,0,188,189,5,110,0,0,189,26,1,0,0,0,190,191,5,115,0,0,191,192,5,
        116,0,0,192,193,5,114,0,0,193,194,5,105,0,0,194,195,5,110,0,0,195,
        196,5,103,0,0,196,28,1,0,0,0,197,198,5,118,0,0,198,199,5,111,0,0,
        199,200,5,105,0,0,200,201,5,100,0,0,201,30,1,0,0,0,202,203,5,119,
        0,0,203,204,5,104,0,0,204,205,5,105,0,0,205,206,5,108,0,0,206,207,
        5,101,0,0,207,32,1,0,0,0,208,210,7,0,0,0,209,208,1,0,0,0,210,211,
        1,0,0,0,211,209,1,0,0,0,211,212,1,0,0,0,212,34,1,0,0,0,213,217,5,
        46,0,0,214,216,7,0,0,0,215,214,1,0,0,0,216,219,1,0,0,0,217,215,1,
        0,0,0,217,218,1,0,0,0,218,36,1,0,0,0,219,217,1,0,0,0,220,222,7,1,
        0,0,221,223,7,2,0,0,222,221,1,0,0,0,222,223,1,0,0,0,223,225,1,0,
        0,0,224,226,7,0,0,0,225,224,1,0,0,0,226,227,1,0,0,0,227,225,1,0,
        0,0,227,228,1,0,0,0,228,38,1,0,0,0,229,230,3,33,16,0,230,232,3,35,
        17,0,231,233,3,37,18,0,232,231,1,0,0,0,232,233,1,0,0,0,233,40,1,
        0,0,0,234,236,7,0,0,0,235,234,1,0,0,0,236,237,1,0,0,0,237,235,1,
        0,0,0,237,238,1,0,0,0,238,42,1,0,0,0,239,240,5,116,0,0,240,241,5,
        114,0,0,241,242,5,117,0,0,242,243,5,101,0,0,243,44,1,0,0,0,244,245,
        5,102,0,0,245,246,5,97,0,0,246,247,5,108,0,0,247,248,5,115,0,0,248,
        249,5,101,0,0,249,46,1,0,0,0,250,253,3,43,21,0,251,253,3,45,22,0,
        252,250,1,0,0,0,252,251,1,0,0,0,253,48,1,0,0,0,254,258,7,3,0,0,255,
        256,5,92,0,0,256,258,7,4,0,0,257,254,1,0,0,0,257,255,1,0,0,0,258,
        50,1,0,0,0,259,263,5,34,0,0,260,262,3,49,24,0,261,260,1,0,0,0,262,
        265,1,0,0,0,263,261,1,0,0,0,263,264,1,0,0,0,264,266,1,0,0,0,265,
        263,1,0,0,0,266,267,5,34,0,0,267,268,6,25,0,0,268,52,1,0,0,0,269,
        270,5,58,0,0,270,54,1,0,0,0,271,272,5,45,0,0,272,273,5,62,0,0,273,
        56,1,0,0,0,274,275,5,62,0,0,275,276,5,62,0,0,276,58,1,0,0,0,277,
        278,5,61,0,0,278,279,5,61,0,0,279,60,1,0,0,0,280,281,5,33,0,0,281,
        282,5,61,0,0,282,62,1,0,0,0,283,284,5,60,0,0,284,285,5,61,0,0,285,
        64,1,0,0,0,286,287,5,60,0,0,287,66,1,0,0,0,288,289,5,62,0,0,289,
        290,5,61,0,0,290,68,1,0,0,0,291,292,5,62,0,0,292,70,1,0,0,0,293,
        294,5,38,0,0,294,295,5,38,0,0,295,72,1,0,0,0,296,297,5,124,0,0,297,
        298,5,124,0,0,298,74,1,0,0,0,299,300,5,33,0,0,300,76,1,0,0,0,301,
        302,5,42,0,0,302,303,5,47,0,0,303,304,1,0,0,0,304,305,6,38,1,0,305,
        78,1,0,0,0,306,307,5,61,0,0,307,80,1,0,0,0,308,309,5,43,0,0,309,
        82,1,0,0,0,310,311,5,45,0,0,311,84,1,0,0,0,312,313,5,42,0,0,313,
        86,1,0,0,0,314,315,5,47,0,0,315,88,1,0,0,0,316,317,5,37,0,0,317,
        90,1,0,0,0,318,319,5,40,0,0,319,92,1,0,0,0,320,321,5,41,0,0,321,
        94,1,0,0,0,322,323,5,123,0,0,323,96,1,0,0,0,324,325,5,125,0,0,325,
        98,1,0,0,0,326,327,5,91,0,0,327,100,1,0,0,0,328,329,5,93,0,0,329,
        102,1,0,0,0,330,331,5,44,0,0,331,104,1,0,0,0,332,333,5,59,0,0,333,
        106,1,0,0,0,334,335,5,46,0,0,335,108,1,0,0,0,336,340,7,5,0,0,337,
        339,7,6,0,0,338,337,1,0,0,0,339,342,1,0,0,0,340,338,1,0,0,0,340,
        341,1,0,0,0,341,110,1,0,0,0,342,340,1,0,0,0,343,345,7,7,0,0,344,
        343,1,0,0,0,345,346,1,0,0,0,346,344,1,0,0,0,346,347,1,0,0,0,347,
        348,1,0,0,0,348,349,6,55,2,0,349,112,1,0,0,0,350,351,5,47,0,0,351,
        352,5,47,0,0,352,356,1,0,0,0,353,355,8,8,0,0,354,353,1,0,0,0,355,
        358,1,0,0,0,356,354,1,0,0,0,356,357,1,0,0,0,357,359,1,0,0,0,358,
        356,1,0,0,0,359,360,6,56,2,0,360,114,1,0,0,0,361,362,5,47,0,0,362,
        363,5,42,0,0,363,368,1,0,0,0,364,367,3,115,57,0,365,367,9,0,0,0,
        366,364,1,0,0,0,366,365,1,0,0,0,367,370,1,0,0,0,368,369,1,0,0,0,
        368,366,1,0,0,0,369,371,1,0,0,0,370,368,1,0,0,0,371,372,5,42,0,0,
        372,373,5,47,0,0,373,374,1,0,0,0,374,375,6,57,3,0,375,116,1,0,0,
        0,376,380,5,34,0,0,377,379,3,49,24,0,378,377,1,0,0,0,379,382,1,0,
        0,0,380,378,1,0,0,0,380,381,1,0,0,0,381,383,1,0,0,0,382,380,1,0,
        0,0,383,384,5,92,0,0,384,385,8,4,0,0,385,386,6,58,4,0,386,118,1,
        0,0,0,387,391,5,34,0,0,388,390,3,49,24,0,389,388,1,0,0,0,390,393,
        1,0,0,0,391,389,1,0,0,0,391,392,1,0,0,0,392,397,1,0,0,0,393,391,
        1,0,0,0,394,398,7,9,0,0,395,396,5,13,0,0,396,398,5,10,0,0,397,394,
        1,0,0,0,397,395,1,0,0,0,398,399,1,0,0,0,399,400,6,59,5,0,400,120,
        1,0,0,0,401,402,9,0,0,0,402,122,1,0,0,0,18,0,211,217,222,227,232,
        237,252,257,263,340,346,356,366,368,380,391,397,6,1,25,0,1,38,1,
        6,0,0,1,57,2,1,58,3,1,59,4
    ]

class HLangLexer(Lexer):

    atn = ATNDeserializer().deserialize(serializedATN())

    decisionsToDFA = [ DFA(ds, i) for i, ds in enumerate(atn.decisionToState) ]

    BOOL = 1
    BREAK = 2
    CONST = 3
    CONTINUE = 4
    ELSE = 5
    FLOAT = 6
    FOR = 7
    FUNC = 8
    IF = 9
    IN = 10
    INT = 11
    LET = 12
    RETURN = 13
    STRING = 14
    VOID = 15
    WHILE = 16
    FLOAT_LIT = 17
    INT_LIT = 18
    BOOLEAN_LIT = 19
    STRING_LIT = 20
    COLON = 21
    RETURN_TYPE = 22
    PIPELINE = 23
    EQ = 24
    DIFF = 25
    LTE = 26
    LT = 27
    GTE = 28
    GT = 29
    AND = 30
    OR = 31
    NOT = 32
    ASTERISK = 33
    ASSIGN = 34
    ADD = 35
    SUB = 36
    MUL = 37
    DIV = 38
    MOD = 39
    LP = 40
    RP = 41
    LBRACE = 42
    RBRACE = 43
    LSB = 44
    RSB = 45
    COMMA = 46
    SMCOLON = 47
    DOT = 48
    ID = 49
    WS = 50
    SINGLE_LINE_COMMENT = 51
    MULTI_LINE_COMMENT = 52
    ILLEGAL_ESCAPE = 53
    UNCLOSE_STRING = 54
    ERROR_CHAR = 55

    channelNames = [ u"DEFAULT_TOKEN_CHANNEL", u"HIDDEN" ]

    modeNames = [ "DEFAULT_MODE" ]

    literalNames = [ "<INVALID>",
            "'bool'", "'break'", "'const'", "'continue'", "'else'", "'float'", 
            "'for'", "'func'", "'if'", "'in'", "'int'", "'let'", "'return'", 
            "'string'", "'void'", "'while'", "':'", "'->'", "'>>'", "'=='", 
            "'!='", "'<='", "'<'", "'>='", "'>'", "'&&'", "'||'", "'!'", 
            "'*/'", "'='", "'+'", "'-'", "'*'", "'/'", "'%'", "'('", "')'", 
            "'{'", "'}'", "'['", "']'", "','", "';'", "'.'" ]

    symbolicNames = [ "<INVALID>",
            "BOOL", "BREAK", "CONST", "CONTINUE", "ELSE", "FLOAT", "FOR", 
            "FUNC", "IF", "IN", "INT", "LET", "RETURN", "STRING", "VOID", 
            "WHILE", "FLOAT_LIT", "INT_LIT", "BOOLEAN_LIT", "STRING_LIT", 
            "COLON", "RETURN_TYPE", "PIPELINE", "EQ", "DIFF", "LTE", "LT", 
            "GTE", "GT", "AND", "OR", "NOT", "ASTERISK", "ASSIGN", "ADD", 
            "SUB", "MUL", "DIV", "MOD", "LP", "RP", "LBRACE", "RBRACE", 
            "LSB", "RSB", "COMMA", "SMCOLON", "DOT", "ID", "WS", "SINGLE_LINE_COMMENT", 
            "MULTI_LINE_COMMENT", "ILLEGAL_ESCAPE", "UNCLOSE_STRING", "ERROR_CHAR" ]

    ruleNames = [ "BOOL", "BREAK", "CONST", "CONTINUE", "ELSE", "FLOAT", 
                  "FOR", "FUNC", "IF", "IN", "INT", "LET", "RETURN", "STRING", 
                  "VOID", "WHILE", "INTEGER_PART", "FRACTIONAL_PART", "EXPONENT_PART", 
                  "FLOAT_LIT", "INT_LIT", "TRUE", "FALSE", "BOOLEAN_LIT", 
                  "STRINGPRIME", "STRING_LIT", "COLON", "RETURN_TYPE", "PIPELINE", 
                  "EQ", "DIFF", "LTE", "LT", "GTE", "GT", "AND", "OR", "NOT", 
                  "ASTERISK", "ASSIGN", "ADD", "SUB", "MUL", "DIV", "MOD", 
                  "LP", "RP", "LBRACE", "RBRACE", "LSB", "RSB", "COMMA", 
                  "SMCOLON", "DOT", "ID", "WS", "SINGLE_LINE_COMMENT", "MULTI_LINE_COMMENT", 
                  "ILLEGAL_ESCAPE", "UNCLOSE_STRING", "ERROR_CHAR" ]

    grammarFileName = "HLang.g4"

    def __init__(self, input=None, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.1")
        self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
        self._actions = None
        self._predicates = None


    def __init__(self, input=None, output:TextIO = sys.stdout):
        super().__init__(input, output)
        self.checkVersion("4.13.2")
        self._interp = LexerATNSimulator(self, self.atn, self.decisionsToDFA, PredictionContextCache())
        self._actions = None
        self._predicates = None
        self.preType = None


    def emit(self):
        tk = self.type
        self.preType = tk
        if tk == self.UNCLOSE_STRING:       
            result = super().emit();
            raise UncloseString(result.text);
        elif tk == self.ILLEGAL_ESCAPE:
            result = super().emit();
            raise IllegalEscape(result.text);
        elif tk == self.ERROR_CHAR:
            result = super().emit();
            raise ErrorToken(result.text); 
        else:
            return super().emit();


    def action(self, localctx:RuleContext, ruleIndex:int, actionIndex:int):
        if self._actions is None:
            actions = dict()
            actions[25] = self.STRING_LIT_action 
            actions[38] = self.ASTERISK_action 
            actions[57] = self.MULTI_LINE_COMMENT_action 
            actions[58] = self.ILLEGAL_ESCAPE_action 
            actions[59] = self.UNCLOSE_STRING_action 
            self._actions = actions
        action = self._actions.get(ruleIndex, None)
        if action is not None:
            action(localctx, actionIndex)
        else:
            raise Exception("No registered action for:" + str(ruleIndex))


    def STRING_LIT_action(self, localctx:RuleContext , actionIndex:int):
        if actionIndex == 0:
             
                self.text = self.text[1:-1] 

     

    def ASTERISK_action(self, localctx:RuleContext , actionIndex:int):
        if actionIndex == 1:
             self.type = self.ERROR_CHAR
     

    def MULTI_LINE_COMMENT_action(self, localctx:RuleContext , actionIndex:int):
        if actionIndex == 2:

            counter = 0
            i = 0
            n = len(self.text)

            while i < n:
                if (i + 1) < n and self.text[i:i+2] == '/*':
                    counter += 1
                    i += 2
                elif (i + 1) < n and self.text[i:i+2] == '*/':
                    counter -= 1
                    if counter < 0:
                        break
                    i += 2
                else:
                    i += 1
            if counter == 0:
                self.skip()
            else:
                self.type = self.ERROR_CHAR

     

    def ILLEGAL_ESCAPE_action(self, localctx:RuleContext , actionIndex:int):
        if actionIndex == 3:
             self.text = self.text[1:] 
     

    def UNCLOSE_STRING_action(self, localctx:RuleContext , actionIndex:int):
        if actionIndex == 4:
             self.text = self.text[1:] 
     


